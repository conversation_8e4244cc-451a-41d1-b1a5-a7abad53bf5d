"""
effect: |
  在Manim场景中创建并播放一个分步骤讲解的动画。
  左侧展示步骤节点，每一步包含序号和操作描述，节点动态生成并向上移动。
  右侧展示每一步的具体内容（markdown格式），支持代码、列表、文本等。
  最后所有步骤节点缩放移动到画面正中，展示整体概念。

use_cases:
  - 教学演示中的分步骤讲解，如算法步骤、操作流程等
  - 产品功能介绍，逐步展示各个功能点
  - 项目开发流程演示，突出每个阶段的重点

params:
  scene:
    type: FeynmanScene
    desc: Manim场景实例（由系统自动传入）
  steps:
    type: list[StepData | dict[str, Any]]
    desc: 步骤列表。每个元素包含step_number(步骤序号), title(步骤标题), content(步骤内容，markdown格式), color(节点颜色，可选), narration(步骤旁白，可选)等属性
    required: true
  intro_narration:
    type: str
    desc: 开场介绍语音旁白文本
    default: None
  outro_narration:
    type: str
    desc: 结尾总结语音旁白文本
    default: None
  title:
    type: str
    desc: 整体标题
    default: None
  subtitle:
    type: str
    desc: 副标题
    default: None
  id:
    type: str
    desc: 创建的Manim Mobject的唯一标识符
    default: None

dsl_examples:
  - type: animate_step_by_step
    params:
      steps:
        - step_number: "1"
          title: "初始化数据"
          content: |
            ## 创建数组
            ```python
            arr = [64, 34, 25, 12, 22, 11, 90]
            ```
            - 准备待排序的数组
            - 记录数组长度
          color: "#FF6B6B"
          narration: "首先我们初始化一个待排序的数组"
        - step_number: "2"
          title: "选择最小元素"
          content: |
            ## 查找最小值
            ```python
            min_idx = 0
            for i in range(1, len(arr)):
                if arr[i] < arr[min_idx]:
                    min_idx = i
            ```
            - 遍历未排序部分
            - 找到最小元素的索引
          color: "#4ECDC4"
          narration: "接下来在未排序部分找到最小的元素"
        - step_number: "3"
          title: "交换元素"
          content: |
            ## 元素交换
            ```python
            arr[0], arr[min_idx] = arr[min_idx], arr[0]
            ```
            - 将最小元素移到已排序部分的末尾
            - 扩大已排序区域
          color: "#45B7D1"
          narration: "然后将最小元素与第一个位置交换"
      title: "选择排序算法演示"
      subtitle: "逐步理解排序过程"
      intro_narration: "今天我们来学习选择排序算法的工作原理"
      outro_narration: "通过这三个步骤，我们完成了选择排序的一轮操作"

notes:
  - 步骤按数组中的顺序呈现，每个步骤的内容支持完整的markdown语法
  - 左侧节点会动态生成并向上移动，右侧内容会淡出后显示新内容
  - 可以为每个步骤指定颜色，或使用默认颜色方案
  - 最后所有步骤节点会缩放移动到画面中央，形成整体概览
  - 与timeline的差别是，step_by_step需要讲解每个步骤中的详细内容，因此更适合例子讲解等场景，而timeline只是展示事件的整体脉络
"""
from dataclasses import dataclass
from typing import TYPE_CHECKING, Any, Optional

from loguru import logger
from manim import *

from dsl.v2.animation_functions.animate_markdown import _animate_markdown_mobjects, _create_markdown_mobjects
from dsl.v2.themes.theme_utils import ThemeUtils
from utils.format import wrap_text
from utils.md_to_pango import MarkdownToSimplifiedConverter

if TYPE_CHECKING:
    from dsl.v2.core.scene import FeynmanScene


@dataclass
class StepData:
    """步骤数据类"""

    step_number: str
    title: str
    content: str
    color: str
    narration: Optional[str] = None


@dataclass
class StepElement:
    """步骤节点元素类"""

    group: Group
    node: Circle
    number: Text
    title: Text
    color: str


def create_step_element(step_data: StepData, colors: dict[str, str], node_radius: float = 0.5) -> StepElement:
    """创建单个步骤节点元素

    Args:
        step_data: 步骤数据
        colors: 颜色配置字典
        node_radius: 节点半径

    Returns:
        创建好的步骤元素对象
    """
    # 如果步骤没有指定颜色，则使用默认颜色
    step_color = ManimColor(step_data.color or colors.get("step_default", "#4B90E2"))

    # 创建节点
    node = Circle(radius=node_radius, color=step_color)
    node.set_fill(step_color, opacity=1)
    stroke_width = ThemeUtils.get_component_style("step_by_step", "node_stroke_width", 5)
    stroke_color = ThemeUtils.get_color("background", DARK_GRAY)
    node.set_stroke(stroke_color, width=stroke_width)

    # 步骤序号
    number_font_size = ThemeUtils.get_font_size("h3", 32)
    number_font = ThemeUtils.get_font("primary", "Microsoft YaHei")
    number_text_color = WHITE  # 序号使用白色以确保在颜色节点上可见
    number_text = Text(
        step_data.step_number, font_size=number_font_size, weight=BOLD, color=number_text_color, font=number_font
    )
    number_text.move_to(node.get_center())

    # 步骤标题
    title_str = step_data.title or ""
    title_font_size = ThemeUtils.get_font_size("body", 24)
    title_font = ThemeUtils.get_font("primary", "Microsoft YaHei")
    title_color = colors["text_primary"]
    wrapped_title_str = wrap_text(title_str, 20)
    title_text = Text(
        "\n".join(wrapped_title_str),
        font_size=title_font_size,
        weight=BOLD,
        color=title_color,
        font=title_font,
    )

    # 布局
    title_spacing = ThemeUtils.get_spacing("step_title", 0.5)
    title_text.next_to(node, RIGHT, buff=title_spacing)

    # 组合所有元素
    element_group = Group(node, number_text, title_text)

    return StepElement(
        group=element_group,
        node=node,
        number=number_text,
        title=title_text,
        color=str(step_color),
    )


def _normalize_steps(steps: list[StepData | dict[str, Any]]) -> list[StepData]:
    """将步骤数据标准化为StepData对象

    Args:
        steps: 原始步骤数据列表

    Returns:
        标准化后的StepData对象列表
    """
    normalized_steps: list[StepData] = []
    # 使用主题定义的步骤颜色列表
    default_colors = ThemeUtils.get_cyclic_colors(len(steps))

    for i, step_data in enumerate(steps):
        if isinstance(step_data, dict):
            normalized_steps.append(
                StepData(
                    step_number=step_data.get("step_number", str(i + 1)),
                    title=step_data.get("title", ""),
                    content=step_data.get("content", ""),
                    color=step_data.get("color", "") or default_colors[i % len(default_colors)],
                    narration=step_data.get("narration"),
                )
            )
        elif isinstance(step_data, StepData):
            if step_data.color is None:
                step_data.color = default_colors[i % len(default_colors)]
            normalized_steps.append(step_data)
        else:
            logger.warning(f"跳过无效步骤数据: {step_data}")

    return normalized_steps


def _create_title_group(title: Optional[str], subtitle: Optional[str], text_color: str) -> Optional[VGroup]:
    """创建标题组

    Args:
        title: 主标题文本
        subtitle: 副标题文本
        text_color: 文本主色调

    Returns:
        标题组VGroup对象
    """
    if not title:
        return None

    title_font_size = ThemeUtils.get_font_size("h1", 52)
    title_font = ThemeUtils.get_font("primary", "Microsoft YaHei")
    title_color = ThemeUtils.get_color("text_primary", text_color)

    main_title = Text(
        title,
        font_size=title_font_size,
        weight=BOLD,
        color=title_color,
        font=title_font,
    )

    if subtitle:
        subtitle_font_size = ThemeUtils.get_font_size("body", 24)
        subtitle_font = ThemeUtils.get_font("primary", "Microsoft YaHei")
        subtitle_color = ThemeUtils.get_color("text_secondary", text_color)
        subtitle_text = Text(subtitle, font_size=subtitle_font_size, color=subtitle_color, font=subtitle_font)

        title_spacing = ThemeUtils.get_spacing("title_subtitle", 0.3)
        title_group = VGroup(main_title, subtitle_text)
        title_group.arrange(DOWN, buff=title_spacing)
    else:
        title_group = VGroup(main_title)

    title_edge_buff = ThemeUtils.get_spacing("title_edge", 0.5)
    title_group.to_edge(UP, buff=title_edge_buff)
    return title_group


def _create_content_mobjects(content: str) -> tuple[Group, list[dict[str, Any]]]:
    """创建步骤内容的Mobject

    Args:
        content: markdown格式的内容

    Returns:
        内容Mobject组
    """
    converter = MarkdownToSimplifiedConverter()
    parsed_markdown = converter.convert(content)

    if not parsed_markdown:
        logger.info(f"步骤内容为空: {content}")
        return Group()

    content_group = _create_markdown_mobjects(parsed_markdown)
    return content_group, parsed_markdown


def _animate_step_by_step_elements(
    scene: "FeynmanScene",
    step_elements: list[StepElement],
    normalized_steps: list[StepData],
    title_group: Optional[VGroup],
    intro_narration: Optional[str],
    outro_narration: Optional[str],
) -> Group:
    """创建并播放分步骤动画

    Args:
        scene: 场景对象
        step_elements: 步骤元素列表
        normalized_steps: 标准化的步骤数据
        title_group: 标题组对象
        intro_narration: 开场旁白文本
        outro_narration: 结尾旁白文本

    Returns:
        包含所有元素的组合对象
    """
    displayed_steps: list[StepElement] = []
    current_content_group: Optional[Group] = None

    def _play_title_animation():
        if title_group:
            title_animation_duration = ThemeUtils.get_animation_duration("step_title", 2)
            title_shift_amount = ThemeUtils.get_component_style("step_by_step", "title_shift_amount", 0.2)
            title_lag_ratio = ThemeUtils.get_component_style("step_by_step", "title_lag_ratio", 0.3)
            title_wait_time = ThemeUtils.get_animation_duration("step_title_wait", 0.5)

            scene.play(
                LaggedStart(
                    Write(title_group[0]),
                    FadeIn(title_group[1], shift=UP * title_shift_amount) if len(title_group) > 1 else Wait(0),
                    lag_ratio=title_lag_ratio,
                ),
                run_time=title_animation_duration,
            )
            scene.wait(title_wait_time)

    if intro_narration:
        with scene.voiceover(intro_narration) as tracker:  # noqa
            _play_title_animation()
    else:
        _play_title_animation()

    def _play_step_animation(total_duration: Optional[float] = None):
        nonlocal current_content_group

        # 计算左侧步骤区域和右侧内容区域
        screen_width = config.frame_width
        screen_height = config.frame_height

        # 右侧区域：占屏幕宽度的65%
        right_area_width = screen_width * 0.65
        right_area_left_x = -screen_width / 2 + screen_width * 0.35

        # 步骤节点的垂直间距
        step_spacing = ThemeUtils.get_component_style("step_by_step", "step_spacing", 1.5)

        for i, (step_element, step_data) in enumerate(zip(step_elements, normalized_steps)):
            # 1. 移动已有步骤节点向上
            if i > 0:
                move_animations = []
                for displayed_step in displayed_steps:
                    move_animations.append(displayed_step.group.animate.shift(UP * step_spacing))

                if move_animations:
                    move_duration = ThemeUtils.get_animation_duration("step_move", 0.5)
                    scene.play(*move_animations, run_time=move_duration)

            # 2. 新步骤节点出现在左侧中间位置
            step_center_y = 0  # 屏幕中央
            step_element.group.align_to([-config.frame_width / 2, step_center_y, 0], LEFT).shift(RIGHT * SMALL_BUFF)

            # 步骤节点入场动画
            node_animation_duration = ThemeUtils.get_animation_duration("step_node", 0.8)
            scene.play(
                LaggedStart(
                    GrowFromCenter(step_element.node),
                    FadeIn(step_element.number, scale=0.8),
                    Write(step_element.title),
                    lag_ratio=0.3,
                ),
                run_time=node_animation_duration,
            )

            displayed_steps.append(step_element)

            # 3. 右侧内容淡出（如果有之前的内容）
            if current_content_group:
                fadeout_duration = ThemeUtils.get_animation_duration("content_fadeout", 0.3)
                scene.play(FadeOut(current_content_group), run_time=fadeout_duration)

            # 4. 创建并显示新的内容
            content_group, parsed_markdown = _create_content_mobjects(step_data.content)
            if content_group and content_group.submobjects:
                # 定位到右侧区域
                content_group.align_to([right_area_left_x, 0, 0], LEFT)

                # 缩放以适应右侧区域
                max_width = right_area_width * 0.9
                max_height = screen_height * 0.8
                scale_factor = min(
                    max_width / content_group.width if content_group.width > 0 else 1,
                    max_height / content_group.height if content_group.height > 0 else 1,
                    1.0,
                )
                content_group.scale(scale_factor).next_to(title_group, DOWN, buff=MED_LARGE_BUFF, coor_mask=[0, 1, 0])

                # 内容入场动画
                if step_data.narration:
                    with scene.voiceover(step_data.narration) as tracker:  # noqa
                        _animate_markdown_mobjects(scene, content_group, {}, parsed_markdown)
                else:
                    _animate_markdown_mobjects(scene, content_group, {}, parsed_markdown)

                current_content_group = content_group

            # 5. 等待一段时间
            step_wait_time = ThemeUtils.get_animation_duration("step_wait", 0.5)
            scene.wait(step_wait_time)

    _play_step_animation()

    def _play_final_animation():
        # 右侧内容淡出
        if current_content_group:
            fadeout_duration = ThemeUtils.get_animation_duration("final_content_fadeout", 0.5)
            scene.play(FadeOut(current_content_group), run_time=fadeout_duration)

        # 所有步骤节点移动到画面中央并缩放
        if displayed_steps:
            # 创建步骤节点组
            all_steps_group = Group()
            for step in displayed_steps:
                all_steps_group.add(step.group)

            # 重新排列步骤节点
            dest = all_steps_group.copy()
            final_spacing = ThemeUtils.get_component_style("step_by_step", "final_spacing", 1.2)
            dest.arrange(DOWN, buff=final_spacing, coor_mask=[0, 1, 0])

            # 缩放和移动到中央
            final_animation_duration = ThemeUtils.get_animation_duration("final_arrangement", 1.0)
            final_scale = min((config.frame_height - title_group.height) * 0.9 / dest.height, 1.0)
            dest.scale(final_scale).next_to(title_group, DOWN, buff=MED_LARGE_BUFF, coor_mask=[0, 1, 0])

            scene.play(
                Transform(all_steps_group, dest),
                run_time=final_animation_duration,
            )

            final_wait_time = ThemeUtils.get_animation_duration("final_wait", 1.0)
            scene.wait(final_wait_time)

        # 组合所有元素
        if title_group and displayed_steps:
            all_elements_group = Group()
            all_elements_group.add(title_group)
            for step in displayed_steps:
                all_elements_group.add(step.group)
            return all_elements_group
        elif displayed_steps:
            all_elements_group = Group()
            for step in displayed_steps:
                all_elements_group.add(step.group)
            return all_elements_group
        else:
            return Group()

    try:
        if outro_narration:
            with scene.voiceover(outro_narration) as tracker:  # noqa
                return _play_final_animation()
        else:
            return _play_final_animation()
    except Exception as e:
        logger.warning(f"语音功能不可用，跳过outro_narration: {e}")
        return _play_final_animation()


def animate_step_by_step(
    scene: "FeynmanScene",
    steps: list[StepData | dict[str, Any]],
    intro_narration: Optional[str] = None,
    outro_narration: Optional[str] = None,
    title: Optional[str] = None,
    subtitle: Optional[str] = None,
    id: Optional[str] = None,
) -> None:
    """分步骤讲解动画函数

    Args:
        scene: Manim场景实例
        steps: 步骤列表
        intro_narration: 开场介绍语音旁白文本
        outro_narration: 结尾总结语音旁白文本
        title: 整体标题
        subtitle: 副标题
        id: 创建的Manim Mobject的唯一标识符
    """
    logger.info(f"Animating step-by-step with {len(steps)} steps in region 'full_screen'...")
    scene.clear_current_mobj()
    unique_id = id or f"step_by_step_{abs(hash(str(steps))) % 10000}"

    # 检查步骤是否为空
    if not steps:
        logger.warning("Steps list is empty. Nothing to show.")
        return None

    # 使用主题化的颜色配置
    colors = {
        "bg": ThemeUtils.get_color("step_background", "#F7F6F1"),
        "text_primary": ThemeUtils.get_color("text_primary", "#333333"),
        "text_secondary": ThemeUtils.get_color("text_secondary", "#666666"),
        "step_default": ThemeUtils.get_color("step_default_color", "#4B90E2"),
    }

    # 设置参数 - 使用主题化的尺寸
    node_radius = ThemeUtils.get_component_style("step_by_step", "node_radius", 0.5)

    # 标准化步骤数据为StepData对象
    normalized_steps = _normalize_steps(steps)
    if not normalized_steps:
        logger.warning("没有步骤数据可以动画展示.")
        return

    target_rect = scene.full_screen_rect
    if not target_rect:
        logger.error("目标区域 'full_screen' 未找到. 无法放置步骤动画.")
        return

    # 创建标题（如果提供）
    title_group = _create_title_group(title, subtitle, colors["text_primary"])

    # 创建所有步骤元素
    step_elements = []
    for step_data in normalized_steps:
        step_elements.append(create_step_element(step_data, colors, node_radius))

    # 缩放步骤元素，不超过 config.frame_width * 0.3
    max_width = config.frame_width * 0.3
    step_element_scale_factor = 1.0
    for step_element in step_elements:
        step_element_scale_factor = min(max_width / step_element.group.width, step_element_scale_factor)

    for step_element in step_elements:
        step_element.group.scale(step_element_scale_factor)

    # 动画展示步骤
    result_group = _animate_step_by_step_elements(
        scene,
        step_elements,
        normalized_steps,
        title_group,
        intro_narration,
        outro_narration,
    )

    # 保存结果并更新当前场景中的对象
    scene.current_mobj = result_group
    logger.info(f"Step-by-step '{unique_id}' animation complete.")

    return None
