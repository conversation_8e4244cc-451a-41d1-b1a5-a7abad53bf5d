"""
effect: |
    使用Transform动画展示文字内容，支持多种变换效果和样式的平滑过渡。

use_cases:
    - 展示标题文字并应用变换效果
    - 创建文字的渐变、旋转、缩放等动画效果
    - 实现文字内容的平滑切换和过渡
    - 展示重要概念或关键词的强调效果

params:
  scene:
    type: FeynmanScene
    desc: Manim场景实例（由系统自动传入）
  text:
    type: str
    desc: 要显示的文字内容，支持Markdown格式
    required: true
  id:
    type: str
    desc: 创建的Manim Mobject的唯一标识符
    default: None
  narration:
    type: str
    desc: 播放动画时同步播放的语音旁白文本
    required: true
  transform_style:
    type: str
    desc: Transform动画的样式，支持 'fade', 'rotate', 'scale', 'slide', 'morph'
    default: fade
  font_size:
    type: int
    desc: 字体大小
    default: 48
  color:
    type: str
    desc: 文字颜色（十六进制或颜色名称），默认为深色系
    default: BLACK
  position:
    type: str
    desc: 文字位置，可选值：'center', 'top', 'bottom', 'left', 'right'
    default: center
  duration:
    type: float
    desc: Transform动画的持续时间（秒）
    default: 1.0

dsl_examples:
  - type: animate_text_transform
    params:
      text: "欢迎来到Manim世界"
      transform_style: fade
      font_size: 64
      color: DARK_BLUE
      narration: 让我们开始学习Manim动画制作。
  - type: animate_text_transform
    params:
      text: |
        ## 重要概念
        - 动画制作
        - 数学可视化
        - 编程教学
      id: key_concepts
      transform_style: rotate
      position: center
      duration: 1.5
      color: DARK_GRAY
      narration: 这里是我们要讲解的核心概念。
  - type: animate_text_transform
    params:
      text: "Transform动画演示"
      transform_style: scale
      color: MAROON_B
      position: top
      narration: 观察文字的缩放变换效果。
  - type: animate_text_transform
    params:
      text: "滑动效果展示"
      transform_style: slide
      font_size: 32
      position: bottom
      color: DARK_BROWN
      narration: 文字从侧面滑入的效果。

notes:
  - Transform动画的持续时间可以根据内容复杂度调整
  - 支持Markdown格式的文本内容
  - 可以设置不同的Transform样式来实现不同的视觉效果
  - 文字会根据指定位置进行定位和展示
  - 默认使用深色字体以确保与浅色背景的良好对比度
  - Transform动画前会自动移除原对象以避免重叠
"""

from typing import TYPE_CHECKING, Literal, Optional

from loguru import logger
from manim import *

if TYPE_CHECKING:
    from dsl.v2.core.scene import FeynmanScene

from dsl.v2.animation_functions.animate_markdown import _create_markdown_mobjects
from dsl.v2.themes.theme_utils import ThemeUtils
from utils.md_to_pango import MarkdownToSimplifiedConverter


def _flatten_content(content):
    """
    递归处理嵌套的内容，将所有内容转换为字符串列表。
    
    Args:
        content: 可能是字符串、列表或字典的内容
        
    Returns:
        list: 字符串列表
    """
    if isinstance(content, str):
        return [content]
    elif isinstance(content, list):
        result = []
        for item in content:
            result.extend(_flatten_content(item))
        return result
    elif isinstance(content, dict):
        # 处理MarkdownToSimplifiedConverter返回的特殊格式
        if item_type := content.get('type'):
            if item_type == 'heading':
                # 处理标题，从content字段中提取纯文本
                heading_content = content.get('content', '')
                # 移除HTML标签，提取纯文本
                import re
                clean_text = re.sub(r'<[^>]+>', '', heading_content).strip()
                level = content.get('level', 1)
                # 根据级别添加#前缀
                prefix = '#' * level + ' '
                return [prefix + clean_text]
            elif item_type == 'list':
                # 处理列表项
                list_content = content.get('content', [])
                result = []
                for list_item in list_content:
                    if isinstance(list_item, dict) and 'text' in list_item:
                        marker = list_item.get('marker', '-')
                        text = list_item.get('text', '')
                        result.append(f"{marker} {text}")
                    else:
                        result.extend(_flatten_content(list_item))
                return result
        
        # 如果是其他类型的字典，尝试获取text或content字段
        text_content = content.get('text', content.get('content', str(content)))
        return _flatten_content(text_content)
    else:
        return [str(content)]


def _create_text_mobject(
    text: str,
    font_size: int = 48,
    color: str = "BLACK",
) -> Mobject:
    """
    创建文字 Manim 对象。

    Args:
        text: 文字内容
        font_size: 字体大小
        color: 文字颜色

    Returns:
        Mobject: 创建的文字对象
    """
    try:
        # 尝试解析为Manim颜色，默认使用深色系列
        try:
            manim_color = eval(color) if isinstance(color, str) and color.upper() in dir() else color
        except:
            manim_color = color

        # 检查是否是Markdown格式
        if isinstance(text, str) and (text.strip().startswith('#') or '\n' in text.strip()):
            # 使用Markdown处理
            converter = MarkdownToSimplifiedConverter()
            simplified_md = converter.convert(text)
            
            # 使用递归方法处理各种数据结构
            text_lines = _flatten_content(simplified_md)
            simplified_md = '\n'.join(text_lines)
            
            text_objs = []
            for line in simplified_md.split('\n'):
                if line.strip():
                    if line.startswith('## '):
                        # 标题
                        title_text = Text(line[3:], font_size=font_size * 1.2, color=manim_color)
                        text_objs.append(title_text)
                    elif line.startswith('# '):
                        # 主标题
                        main_title = Text(line[2:], font_size=font_size * 1.5, color=manim_color)
                        text_objs.append(main_title)
                    elif line.startswith('- '):
                        # 列表项
                        item_text = Text(line[2:], font_size=font_size * 0.8, color=manim_color)
                        text_objs.append(item_text)
                    else:
                        # 普通文本
                        normal_text = Text(line, font_size=font_size, color=manim_color)
                        text_objs.append(normal_text)
            
            if text_objs:
                # 垂直排列所有文字对象
                text_group = VGroup(*text_objs).arrange(DOWN, buff=0.3)
                return text_group
            else:
                return Text("无内容", font_size=font_size, color=manim_color)
        else:
            # 普通文本
            return Text(str(text), font_size=font_size, color=manim_color)
    
    except Exception as e:
        logger.error(f"创建文字对象时发生错误: {e}")
        # 回退到简单文本
        return Text(str(text), font_size=font_size)


def _position_text(text_obj: Mobject, position: str) -> None:
    """
    根据指定位置定位文字对象。

    Args:
        text_obj: 要定位的文字对象
        position: 位置标识符
    """
    if position == "center":
        text_obj.move_to(ORIGIN)
    elif position == "top":
        text_obj.to_edge(UP)
    elif position == "bottom":
        text_obj.to_edge(DOWN)
    elif position == "left":
        text_obj.to_edge(LEFT)
    elif position == "right":
        text_obj.to_edge(RIGHT)
    else:
        # 默认居中
        text_obj.move_to(ORIGIN)


def _create_placeholder_for_transform(target_obj: Mobject, position: str) -> Mobject:
    """
    创建用于Transform的占位符对象。

    Args:
        target_obj: 目标对象
        position: 位置标识符

    Returns:
        Mobject: 占位符对象
    """
    # 创建一个不可见的占位符，大小和目标对象相同
    placeholder = Rectangle(
        width=target_obj.width,
        height=target_obj.height,
        color=BLUE,
        fill_opacity=0,
        stroke_opacity=0
    )
    
    # 定位占位符
    _position_text(placeholder, position)
    
    return placeholder


def _apply_transform_style(
    scene: "FeynmanScene",
    start_obj: Mobject,
    end_obj: Mobject,
    style: str,
    duration: float = 1.0
) -> None:
    """
    应用不同的Transform样式。

    Args:
        scene: 场景对象
        start_obj: 起始对象
        end_obj: 结束对象
        style: Transform样式
        duration: 动画持续时间
    """
    logger.info(f"应用Transform样式: {style}")
    
    if style == "fade":
        # 淡入淡出效果
        scene.play(
            Transform(start_obj, end_obj, run_time=duration),
            run_time=duration
        )
    
    elif style == "rotate":
        # 旋转Transform效果
        scene.play(
            Rotate(start_obj, PI, run_time=duration/2),
            run_time=duration/2
        )
        scene.play(
            Transform(start_obj, end_obj, run_time=duration/2),
            run_time=duration/2
        )
    
    elif style == "scale":
        # 缩放Transform效果
        scene.play(
            start_obj.animate.scale(0.1),
            run_time=duration/2
        )
        scene.play(
            Transform(start_obj, end_obj, run_time=duration/2),
            run_time=duration/2
        )
    
    elif style == "slide":
        # 滑动Transform效果
        original_pos = start_obj.get_center()
        scene.play(
            start_obj.animate.shift(LEFT * 8),
            run_time=duration/3
        )
        scene.play(
            Transform(start_obj, end_obj, run_time=duration/3),
            run_time=duration/3
        )
        scene.play(
            start_obj.animate.move_to(original_pos),
            run_time=duration/3
        )
    
    elif style == "morph":
        # 形变Transform效果
        scene.play(
            Transform(start_obj, end_obj, run_time=duration),
            run_time=duration
        )
    
    else:
        # 默认使用简单Transform
        scene.play(
            Transform(start_obj, end_obj, run_time=duration),
            run_time=duration
        )


def animate_text_transform(
    scene: "FeynmanScene",
    text: str,
    id: Optional[str] = None,
    narration: str = "",
    transform_style: Literal["fade", "rotate", "scale", "slide", "morph"] = "fade",
    font_size: int = 48,
    color: str = "BLACK",
    position: Literal["center", "top", "bottom", "left", "right"] = "center",
    duration: float = 1.0,
) -> None:
    """
    使用Transform动画展示文字内容。

    Args:
        scene: Manim场景实例
        text: 要显示的文字内容
        id: 创建的Manim Mobject的唯一标识符
        narration: 播放动画时同步播放的语音旁白文本
        transform_style: Transform动画的样式
        font_size: 字体大小
        color: 文字颜色
        position: 文字位置
        duration: Transform动画的持续时间
    """
    logger.info(f"开始文字Transform动画: '{text[:20]}...' 样式: {transform_style}")
    
    # 创建唯一的引用 ID
    ref_id = id or f"text_transform_{hash(text) % 10000}"
    
    # 清理当前对象
    scene.clear_current_mobj()
    
    try:
        # 创建文字对象
        text_obj = _create_text_mobject(text, font_size, color)
        
        # 定位文字对象
        _position_text(text_obj, position)
        
        # 使用语音旁白
        with scene.voiceover(narration) as tracker:
            # 如果场景中已有当前对象，进行Transform切换
            if scene.current_mobj is not None:
                # 直接Transform到新内容
                _apply_transform_style(scene, scene.current_mobj, text_obj, transform_style, duration)
            else:
                # 如果没有当前对象，创建一个占位符进行Transform
                placeholder = _create_placeholder_for_transform(text_obj, position)
                scene.add(placeholder)
                
                # 从占位符Transform到文字对象
                _apply_transform_style(scene, placeholder, text_obj, transform_style, duration)
        
        # 更新当前对象引用
        scene.current_mobj = text_obj
        
        logger.info(f"文字Transform动画完成: {ref_id}")
        
    except Exception as e:
        logger.error(f"创建文字Transform动画时发生错误: {e}")
        
        # 创建错误提示文字
        error_text = Text(
            f"文字显示错误: {str(e)[:30]}...",
            font_size=24,
            color=RED
        )
        _position_text(error_text, position)
        
        # 错误情况下也使用Transform显示错误信息
        with scene.voiceover(narration) as tracker:
            if scene.current_mobj is not None:
                _apply_transform_style(scene, scene.current_mobj, error_text, "fade", duration)
            else:
                placeholder = _create_placeholder_for_transform(error_text, position)
                scene.add(placeholder)
                _apply_transform_style(scene, placeholder, error_text, "fade", duration)
        
        scene.current_mobj = error_text 