"""
This package contains all the individual animation function modules.
Importing these modules ensures that their @register_animation decorators are run,
populating the ANIMATION_FUNCTIONS registry in dsl.v2.core.animation_registry.
"""

from .animate_architecture_diagram import animate_architecture_diagram
from .animate_chart import animate_chart
from .animate_counter import animate_counter
from .animate_deep_insight import animate_deep_insight
from .animate_emoji_flowchart import animate_emoji_flowchart
from .animate_highlight_content import animate_highlight_content
from .animate_image import animate_image
from .animate_markdown import animate_markdown
from .animate_mindmap import animate_mindmap
from .animate_qa_cards import animate_qa_cards
from .animate_side_by_side_comparison import animate_side_by_side_comparison
from .animate_step_by_step import animate_step_by_step
from .animate_table import animate_table
from .animate_text_transform import animate_text_transform
from .animate_timeline import animate_timeline
from .animate_universal_display import animate_universal_display
from .animate_video import animate_video

__all__ = [
    "animate_architecture_diagram",
    "animate_chart",
    "animate_counter",
    "animate_deep_insight",
    "animate_emoji_flowchart",
    "animate_timeline",
    "animate_image",
    "animate_markdown",
    "animate_mindmap",
    "animate_qa_cards",
    "animate_video",
    "animate_side_by_side_comparison",
    "animate_step_by_step",
    "animate_highlight_content",
    "animate_table",
    "animate_text_transform",
    "animate_universal_display",
]
