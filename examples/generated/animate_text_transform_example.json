{"schema_version": "2.0-mvp", "metadata": {"title": "animate_text_transform 示例", "author": "自动生成", "background_color": "BLACK"}, "actions": [{"type": "animate_text_transform", "params": {"text": "欢迎来到Man<PERSON>世界", "transform_style": "fade", "font_size": 64, "color": "DARK_BLUE", "narration": "让我们开始学习Manim动画制作。"}}, {"type": "animate_text_transform", "params": {"text": "## 重要概念\n- 动画制作\n- 数学可视化\n- 编程教学\n", "id": "key_concepts", "transform_style": "rotate", "position": "center", "duration": 1.5, "color": "DARK_GRAY", "narration": "这里是我们要讲解的核心概念。"}}, {"type": "animate_text_transform", "params": {"text": "Transform动画演示", "transform_style": "scale", "color": "MAROON_B", "position": "top", "narration": "观察文字的缩放变换效果。"}}, {"type": "animate_text_transform", "params": {"text": "滑动效果展示", "transform_style": "slide", "font_size": 32, "position": "bottom", "color": "DARK_BROWN", "narration": "文字从侧面滑入的效果。"}}, {"type": "animate_text_transform", "params": {"text": "形变效果展示", "transform_style": "morph", "font_size": 32, "position": "left", "color": "DARK_BROWN", "narration": "文字形变的效果。"}}]}