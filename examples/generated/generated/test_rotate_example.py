# Generated by <PERSON><PERSON>man DSL v2 Code Generator
# Author: 测试
# -*- coding: utf-8 -*-

import sys
import os
root_dir = os.getcwd()
sys.path.append(root_dir)

from manim import *
from dsl.v2.core.scene import FeynmanScene
from dsl.v2.animation_functions import *  # IMPORTANT: Ensures all animations are registered

class Scene_(FeynmanScene):
    config.background_color = '#FFFFFF'

    def construct(self):
        self.add_background()

        # Action 1: animate_text_transform
        animate_text_transform(
            scene=self,
            text="普通文字",
            transform_style="fade",
            font_size=64,
            color="DARK_BLUE",
            narration="首先显示普通文字。"
        )

        # Action 2: animate_text_transform
        animate_text_transform(
            scene=self,
            text="旋转入场的文字",
            transform_style="rotate",
            font_size=64,
            color="RED",
            position="center",
            duration=2.0,
            narration="观察这个文字如何整体旋转入场。"
        )

        # Action 3: animate_text_transform
        animate_text_transform(
            scene=self,
            text="## 旋转标题\n- 项目一\n- 项目二",
            transform_style="rotate",
            font_size=48,
            color="DARK_GREEN",
            position="center",
            duration=2.5,
            narration="多行文字的整体旋转效果。"
        )
        # --- Final wait to hold the last frame ---
        self.wait(1)
