# Generated by <PERSON><PERSON>man DSL v2 Code Generator
# Author: 自动生成
# -*- coding: utf-8 -*-

import sys
import os
root_dir = os.getcwd()
sys.path.append(root_dir)

from manim import *
from dsl.v2.core.scene import FeynmanScene
from dsl.v2.animation_functions import *  # IMPORTANT: Ensures all animations are registered

class animate_text_transform_(FeynmanScene):
    config.background_color = '#FFFFFF'

    def construct(self):
        self.add_background()

        # Action 1: animate_text_transform
        animate_text_transform(
            scene=self,
            text="欢迎来到Manim世界",
            transform_style="fade",
            font_size=64,
            color="DARK_BLUE",
            narration="让我们开始学习Manim动画制作。"
        )

        # Action 2: animate_text_transform
        animate_text_transform(
            scene=self,
            text="## 重要概念\n- 动画制作\n- 数学可视化\n- 编程教学\n",
            id="key_concepts",
            transform_style="rotate",
            position="center",
            duration=1.5,
            color="DARK_GRAY",
            narration="这里是我们要讲解的核心概念。"
        )

        # Action 3: animate_text_transform
        animate_text_transform(
            scene=self,
            text="Transform动画演示",
            transform_style="scale",
            color="MAROON_B",
            position="top",
            narration="观察文字的缩放变换效果。"
        )

        # Action 4: animate_text_transform
        animate_text_transform(
            scene=self,
            text="滑动效果展示",
            transform_style="slide",
            font_size=32,
            position="bottom",
            color="DARK_BROWN",
            narration="文字从侧面滑入的效果。"
        )

        # Action 5: animate_text_transform
        animate_text_transform(
            scene=self,
            text="形变效果展示",
            transform_style="morph",
            font_size=32,
            position="left",
            color="DARK_BROWN",
            narration="文字形变的效果。"
        )
        # --- Final wait to hold the last frame ---
        self.wait(1)
