#!/usr/bin/env python3
"""
测试 animate_step_by_step 中的连接线功能
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from dsl.v2.animation_functions.animate_step_by_step import (
    StepData,
    StepElement,
    create_step_element,
    _normalize_steps,
)
from dsl.v2.themes.theme_utils import ThemeUtils

def test_step_element_creation():
    """测试步骤元素创建"""
    print("测试步骤元素创建...")

    # 创建测试数据
    step_data = StepData(
        step_number="1",
        title="测试步骤",
        content="这是一个测试步骤的内容",
        color="#FF6B6B",
        narration="这是测试旁白"
    )

    colors = {
        "text_primary": "#333333",
        "text_secondary": "#666666",
        "step_default": "#4B90E2",
    }

    # 创建步骤元素
    step_element = create_step_element(step_data, colors, node_radius=0.5)

    # 验证步骤元素的属性
    assert isinstance(step_element, StepElement)
    assert step_element.node is not None
    assert step_element.number is not None
    assert step_element.title is not None
    assert step_element.connector is None  # 初始时连接线应该为None
    assert step_element.color == "#FF6B6B"

    print("✓ 步骤元素创建测试通过")

def test_normalize_steps():
    """测试步骤数据标准化"""
    print("测试步骤数据标准化...")

    # 测试字典格式的步骤数据
    steps_dict = [
        {
            "step_number": "1",
            "title": "第一步",
            "content": "第一步的内容",
            "color": "#FF6B6B",
            "narration": "第一步旁白"
        },
        {
            "step_number": "2",
            "title": "第二步",
            "content": "第二步的内容",
            # 没有指定颜色，应该使用默认颜色
        }
    ]

    normalized = _normalize_steps(steps_dict)

    assert len(normalized) == 2
    assert all(isinstance(step, StepData) for step in normalized)
    assert normalized[0].color == "#FF6B6B"
    assert normalized[1].color is not None  # 应该有默认颜色
    print(f"第二个步骤的颜色类型: {type(normalized[1].color)}, 值: {normalized[1].color}")

    print("✓ 步骤数据标准化测试通过")

def test_theme_utils_integration():
    """测试主题工具集成"""
    print("测试主题工具集成...")

    # 测试获取样式配置
    connector_stroke_width = ThemeUtils.get_component_style("step_by_step", "connector_stroke_width", 4)
    step_spacing = ThemeUtils.get_component_style("step_by_step", "step_spacing", 1.5)
    node_radius = ThemeUtils.get_component_style("step_by_step", "node_radius", 0.5)

    assert isinstance(connector_stroke_width, (int, float))
    assert isinstance(step_spacing, (int, float))
    assert isinstance(node_radius, (int, float))

    print("✓ 主题工具集成测试通过")

if __name__ == "__main__":
    print("开始测试 animate_step_by_step 连接线功能...")

    try:
        test_step_element_creation()
        test_normalize_steps()
        test_theme_utils_integration()

        print("\n🎉 所有测试通过！连接线功能已成功添加到 animate_step_by_step 中。")
        print("\n主要改进：")
        print("1. ✓ 在 StepElement 数据类中添加了 connector 字段")
        print("2. ✓ 在步骤动画中添加了连接线创建逻辑")
        print("3. ✓ 连接线样式参考了 timeline 的实现（颜色、线条宽度等）")
        print("4. ✓ 在移动动画中包含了连接线的移动")
        print("5. ✓ 在最终动画中包含了连接线的变换")

    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
