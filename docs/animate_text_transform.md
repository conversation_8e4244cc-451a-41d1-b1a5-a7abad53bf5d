# animate_text_transform

## 效果

使用Transform动画展示文字内容，支持多种变换效果和样式的平滑过渡。


## 使用场景

- 展示标题文字并应用变换效果
- 创建文字的渐变、旋转、缩放等动画效果
- 实现文字内容的平滑切换和过渡
- 展示重要概念或关键词的强调效果

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| text | str | 要显示的文字内容，支持Markdown格式 | 是 | - |
| id | str | 创建的Manim Mobject的唯一标识符 | 否 | None |
| narration | str | 播放动画时同步播放的语音旁白文本 | 是 | - |
| transform_style | str | Transform动画的样式，支持 'fade', 'rotate', 'scale', 'slide', 'morph' | 否 | fade |
| font_size | int | 字体大小 | 否 | 48 |
| color | str | 文字颜色（十六进制或颜色名称），默认为深色系 | 否 | BLACK |
| position | str | 文字位置，可选值：'center', 'top', 'bottom', 'left', 'right' | 否 | center |
| duration | float | Transform动画的持续时间（秒） | 否 | 1.0 |

## DSL示例

### 示例 1

```json
{
  "type": "animate_text_transform",
  "params": {
    "text": "欢迎来到Manim世界",
    "transform_style": "fade",
    "font_size": 64,
    "color": "DARK_BLUE",
    "narration": "让我们开始学习Manim动画制作。"
  }
}
```

### 示例 2

```json
{
  "type": "animate_text_transform",
  "params": {
    "text": "## 重要概念\n- 动画制作\n- 数学可视化\n- 编程教学\n",
    "id": "key_concepts",
    "transform_style": "rotate",
    "position": "center",
    "duration": 1.5,
    "color": "DARK_GRAY",
    "narration": "这里是我们要讲解的核心概念。"
  }
}
```

### 示例 3

```json
{
  "type": "animate_text_transform",
  "params": {
    "text": "Transform动画演示",
    "transform_style": "scale",
    "color": "MAROON_B",
    "position": "top",
    "narration": "观察文字的缩放变换效果。"
  }
}
```

### 示例 4

```json
{
  "type": "animate_text_transform",
  "params": {
    "text": "滑动效果展示",
    "transform_style": "slide",
    "font_size": 32,
    "position": "bottom",
    "color": "DARK_BROWN",
    "narration": "文字从侧面滑入的效果。"
  }
}
```

## 注意事项

- Transform动画的持续时间可以根据内容复杂度调整
- 支持Markdown格式的文本内容
- 可以设置不同的Transform样式来实现不同的视觉效果
- 文字会根据指定位置进行定位和展示
- 默认使用深色字体以确保与浅色背景的良好对比度
- Transform动画前会自动移除原对象以避免重叠

