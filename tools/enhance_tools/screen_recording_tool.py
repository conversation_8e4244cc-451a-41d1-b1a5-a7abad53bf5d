#!/usr/bin/env python3
"""
录屏工具 - 多模态呈现类
专门用于开场基本信息视频介绍
"""

import os
import re
from typing import Any, Optional

from loguru import logger

from .base_tool import EnhancementTool, ToolCategory


class ScreenRecordingTool(EnhancementTool):
    """录屏工具 - 多模态呈现类，专门用于开场基本信息视频介绍"""

    tool_name = "screen_recording"
    tool_description = "自动录制config.yaml中配置的URL页面，生成开场介绍视频。注意：这个工具只能录制配置文件中指定的URL，不会录制用户输入材料中提到的其他URL"
    tool_category = ToolCategory.MULTIMODAL_PRESENTATION

    def __init__(self, config=None):
        self.config = config
        self._initialize_toolkits()

    def _initialize_toolkits(self):
        """初始化录屏工具包（延迟导入）"""
        self.github_toolkit = None
        self.arxiv_toolkit = None

        try:
            from tools.arxiv_recorder_toolkit import ArxivRecorderToolkit
            from tools.github_scroller_toolkit import GithubScrollerToolkit

            self.github_toolkit = GithubScrollerToolkit()
            self.arxiv_toolkit = ArxivRecorderToolkit()
            logger.info("录屏工具包初始化成功")
        except ImportError as e:
            logger.warning(f"录屏工具包导入失败: {e}")

    def get_tool_info(self) -> str:
        """获取工具信息 - 供智能选择器决策使用"""
        return """录屏工具 - 自动生成开场介绍视频

**核心作用**：
本工具专门用于自动录制在 `config.yaml` 文件中 `material.sources` 部分配置并启用的URL（如GitHub仓库、ArXiv论文页面等），生成一段8-12秒的开场介绍视频 (MP4)。这个视频旨在快速展示源材料的核心信息、界面布局或基本特点，作为后续详细内容的视觉引子。

**重要注意事项**：
- **仅处理配置文件URL**：此工具严格只处理 `config.yaml` 中配置的URL。它不会分析或录制用户在主要输入内容中提及的任何其他URL。
- **开场专用**：设计用途是制作简短的开场或概览视频，不适用于录制详细教程或长篇演示。

**适合的场景和内容** (前提是URL在config.yaml中配置并启用)：
- **内容类型**：任何基于配置文件URL源的内容分析、GitHub项目介绍、ArXiv论文解读、一般网页内容概览。
- **目的**：视频开场、项目概览短片、论文摘要视频、演示的起始片段、教学视频的引入部分。

**使用条件 (必须满足)**：
1. `config.yaml` 文件中的 `material.sources` 部分至少有一个源被设置为 `enabled: true`。
2. 该启用的源必须配置了一个有效的URL地址 (以 `http://` 或 `https://` 开头)。
3. `config.yaml` 文件中的 `material.material_enhance.screen_record` (或类似路径下的录屏总开关) 必须设置为 `true`。

**不适合的场景和内容**：
- **内容类型**：纯本地文件分析 (无URL)、Chat模式的对话内容、无任何配置文件URL的纯文本材料。
- **目的**：制作详细技术教程、深度代码走查视频、纯音频内容、生成静态文档。

**无法使用的情况 (阻断条件)**：
- `config.yaml` 中 `material.sources` 下的所有源都被设置为 `disabled: true`。
- 所有启用的源都没有配置有效的URL。
- 全局的录屏开关 (`material.material_enhance.screen_record` 或类似) 设置为 `false`。
- 用户通过其他方式明确禁用了录屏功能。

**输出形式**：一个MP4格式的视频文件，通常命名为 `screen_record.mp4`，时长约8-12秒。

**强烈推荐**：只要配置文件中有启用的URL源，就强烈推荐使用此工具来制作一个引人入胜的开场视频，以提升整体内容的观感和专业度。"""

    def can_apply(self, content: str, purpose: str, context: dict[str, Any]) -> bool:
        """简化的可用性检查 - 只检查基本配置条件"""
        # 检查基本配置
        if not self.config:
            return False

        # 检查config中是否启用录屏
        material_enhance = self.config.get("material", {}).get("material_enhance", {})
        if not material_enhance.get("screen_record", False):
            return False

        # 获取当前启用的源和URL
        sources = self.config.get("material", {}).get("sources", {})
        for source_type, source_config in sources.items():
            if source_config.get("enabled", False):
                url = source_config.get("url", "")
                if url and url.startswith(("http://", "https://")):
                    return True

        return False

    def apply_tool(self, content: str, output_dir: str, context: dict[str, Any], focus: str = None) -> Optional[str]:
        """执行录屏工具"""
        if not self.can_apply(content, context.get("purpose", ""), context):
            return None

        # 获取URL和源类型
        sources = self.config.get("material", {}).get("sources", {})
        url = None
        # Iterate to find the first enabled source with a URL
        for source_config_item in sources.values():
            if source_config_item.get("enabled", False):
                url_candidate = source_config_item.get("url", "")
                if url_candidate and url_candidate.startswith(("http://", "https://")):
                    url = url_candidate
                    break

        if not url:
            logger.warning("录屏工具：未找到配置文件中启用的有效URL。")
            return None

        determined_source_type = self._determine_source_type(url)

        # 转换ArXiv PDF链接为页面链接
        if determined_source_type == "arxiv" and "/pdf/" in url:
            url = url.replace("/pdf/", "/abs/")

        # 生成输出路径
        video_filename = "screen_record.mp4"
        video_path = os.path.join(output_dir, video_filename)
        os.makedirs(output_dir, exist_ok=True)

        # 检查是否已存在文件
        if os.path.exists(video_path):
            logger.info(f"录屏视频已存在: {video_path}")
            return video_path

        # 执行录屏
        try:
            # video_path is the absolute path where the video should be saved
            record_result = self._record_video(url, video_path, determined_source_type)
            if record_result and record_result.get("status") == "success":
                logger.info(f"录屏视频创建成功: {record_result.get('video_path', video_path)}")
                return video_path
            else:
                logger.error(f"录屏失败，工具包返回: {record_result}")
        except Exception as e:
            logger.error(f"录屏过程异常: {e}")

        return None

    def _determine_source_type(self, url: str) -> str:
        """确定录屏源类型"""
        if "github.com" in url:
            return "github"
        elif "arxiv.org" in url:
            return "arxiv"
        else:
            return "github"  # 默认使用github录屏方式

    def _record_video(self, url: str, output_path: str, source_type: str) -> Optional[dict]:
        """执行实际的录屏操作"""
        if source_type == "github" and self.github_toolkit:
            return self.github_toolkit.record_github_scroll_video(
                url=url,
                output_path=output_path,
                duration=12,
                width=1920,
                height=1080,
                fps=15,
                smooth_factor=0.2,
                title_focus=1,
                star_focus=2,
                zoom_factor=2.0,
                readme_pause=1.0,
            )
        elif source_type == "arxiv" and self.arxiv_toolkit:
            return self.arxiv_toolkit.record_arxiv_video(
                url=url,
                output_path=output_path,
                duration=8,
                width=1920,
                height=1080,
                fps=15,
                smooth_factor=0.2,
                title_focus=4,
                zoom_factor=2.0,
                abstract_pause=4.0,
            )
        return None

    def generate_intro(self, tool_result: dict[str, Any]) -> str:
        """生成录屏视频的介绍内容"""
        source_type = tool_result.get("source_type")
        url = tool_result.get("url")
        file_path = tool_result.get("file_path")

        # 统一的开场标识
        opening_emoji = "🎬"

        if source_type == "github":
            # 提取GitHub项目信息
            match = re.search(r"github\.com/([\w\-]+)/([\w\-]+)", url)
            if match:
                owner, repo = match.groups()
                intro = f"## {opening_emoji} 开场视频介绍\n\n"
                intro += f"这是 **{owner}/{repo}** 项目的开场介绍视频，"
                intro += "为您快速展示项目的核心信息、界面布局和基本特点，是了解该项目的最佳起点。\n\n"
                intro += f"📺 **[{repo} 项目开场介绍]({file_path})**\n\n"
                intro += "*💡 建议：这个开场视频将帮助您在深入了解项目细节之前，先建立整体印象*\n\n"
            else:
                intro = f"## {opening_emoji} 开场视频介绍\n\n"
                intro += "为您展示项目的基本信息和整体概况\n\n"
                intro += f"📺 **[项目开场介绍]({file_path})**\n\n"
        elif source_type == "arxiv":
            # 提取ArXiv论文信息
            match = re.search(r"(\d{4}\.\d{5})", url)
            if match:
                paper_id = match.group(1)
                intro = f"## {opening_emoji} 论文开场介绍\n\n"
                intro += f"这是论文 **{paper_id}** 的开场介绍视频，"
                intro += "展示论文的标题、作者、摘要等核心信息，帮助您快速了解论文的基本情况和研究主题。\n\n"
                intro += f"📺 **[论文 {paper_id} 开场介绍]({file_path})**\n\n"
                intro += "*💡 建议：通过这个开场视频，您可以在阅读全文之前快速判断论文的相关性*\n\n"
            else:
                intro = f"## {opening_emoji} 论文开场介绍\n\n"
                intro += "展示论文的基本信息和研究概况\n\n"
                intro += f"📺 **[论文开场介绍]({file_path})**\n\n"
        else:
            intro = f"## {opening_emoji} 内容开场介绍\n\n"
            intro += "这是内容的开场介绍视频，为您快速展示主要信息和基本概况。\n\n"
            intro += f"📺 **[内容开场介绍]({file_path})**\n\n"
            intro += "*💡 这个开场视频将为您提供内容的整体印象*\n\n"

        return intro
